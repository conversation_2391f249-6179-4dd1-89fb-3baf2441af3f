import { HybridPromptAssembler } from '../HybridPromptAssembler';

/**
 * 混合提示词组装器集成测试
 */
function runIntegrationTest() {
  console.log('--- Running Hybrid Prompt Assembler Integration Test ---');

  const assembler = HybridPromptAssembler.getInstance();

  // 测试用例1: 一个简单的查询，只应触发核心模块
  const simpleQuery = '你好，请帮我创建一个简单的Lynx页面';
  console.log(`\n[Test Case 1] Query: "${simpleQuery}"`);
  const prompt1 = assembler.assemblePrompt(simpleQuery);
  console.log('Assembled Prompt Length:', prompt1.length);
  if (prompt1.includes('相关文档参考')) {
    console.error(
      'Test Case 1 FAILED: Optional content was incorrectly included.',
    );
  } else {
    console.log('Test Case 1 PASSED: Only core prompts were included.');
  }

  // 测试用例2: 查询包含明确的扩展关键词 'Canvas'
  const canvasQuery = '如何使用Canvas API绘制一个圆形？';
  console.log(`\n[Test Case 2] Query: "${canvasQuery}"`);
  const prompt2 = assembler.assemblePrompt(canvasQuery);
  console.log('Assembled Prompt Length:', prompt2.length);
  if (prompt2.includes('Canvas绘图系统') && prompt2.includes('相关文档参考')) {
    console.log(
      'Test Case 2 PASSED: Canvas optional content was correctly included.',
    );
  } else {
    console.error('Test Case 2 FAILED: Canvas optional content was missing.');
  }

  // 测试用例3: 查询包含多个扩展关键词 '数据同步' 和 'API'
  const complexQuery = '在页面生命周期中，如何通过setData进行数据同步？';
  console.log(`\n[Test Case 3] Query: "${complexQuery}"`);
  const prompt3 = assembler.assemblePrompt(complexQuery);
  console.log('Assembled Prompt Length:', prompt3.length);
  if (
    prompt3.includes('双线程数据同步机制') &&
    prompt3.includes('Lynx API系统') &&
    prompt3.includes('相关文档参考')
  ) {
    console.log(
      'Test Case 3 PASSED: Multiple RAG contents were correctly included.',
    );
  } else {
    console.error('Test Case 3 FAILED: Required RAG contents are missing.');
  }

  // 测试用例4: 查询一个不存在的关键词
  const nonExistentQuery = '请问如何用React开发？';
  console.log(`\n[Test Case 4] Query: "${nonExistentQuery}"`);
  const prompt4 = assembler.assemblePrompt(nonExistentQuery);
  console.log('Assembled Prompt Length:', prompt4.length);
  if (!prompt4.includes('相关文档参考')) {
    console.log(
      'Test Case 4 PASSED: No RAG content was included for irrelevant query.',
    );
  } else {
    console.error('Test Case 4 FAILED: RAG content was incorrectly included.');
  }

  console.log('\n--- Test Finished ---');
}

// 执行测试
runIntegrationTest();
